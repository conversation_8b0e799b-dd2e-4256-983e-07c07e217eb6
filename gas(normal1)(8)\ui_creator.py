import tkinter as tk
from tkinter import ttk

class UICreator:
    def __init__(self, parent):
        self.parent = parent
        
    def create_main_ui(self):
        """创建主界面"""
        # 创建主容器（水平分割）
        self.parent.main_container = ttk.PanedWindow(self.parent.root, orient=tk.HORIZONTAL)
        self.parent.main_container.pack(fill=tk.BOTH, expand=True)

        # 创建左侧工作区域
        self.parent.work_area = ttk.PanedWindow(self.parent.main_container, orient=tk.VERTICAL)
        self.parent.main_container.add(self.parent.work_area, weight=3)  # 工作区占3份宽度

        # 创建右侧历史记录区域
        self.parent.history_frame = ttk.LabelFrame(self.parent.main_container, text="历史记录")
        self.parent.main_container.add(self.parent.history_frame, weight=1)  # 历史记录占1份宽度
        
        # 创建5栏式界面
        self.create_tab_interface()
        
    def create_side_menu(self):
        """创建侧边菜单栏"""
        menu_container = ttk.Frame(self.parent.root)
        menu_container.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)
        
        # 1. 项目操作分组
        project_ops = ttk.LabelFrame(menu_container, text="项目操作")
        project_ops.pack(fill=tk.X, padx=5, pady=5)
        ttk.Button(project_ops, text="新建项目", command=self.parent.new_project).pack(fill=tk.X, padx=5, pady=3)
        ttk.Button(project_ops, text="保存项目", command=self.parent.save_project).pack(fill=tk.X, padx=5, pady=3)
        ttk.Button(project_ops, text="导出Word", command=self.parent.export_to_word).pack(fill=tk.X, padx=5, pady=3)
        ttk.Button(project_ops, text="导出JSON", command=self.parent.export_to_json).pack(fill=tk.X, padx=5, pady=3)
        ttk.Button(project_ops, text="导入JSON", command=self.parent.import_project_json).pack(fill=tk.X, padx=5, pady=3)
        
        # 2. 计算工具分组
        calc_tools = ttk.LabelFrame(menu_container, text="计算工具")
        calc_tools.pack(fill=tk.X, padx=5, pady=5)
        ttk.Button(calc_tools, text="刷新计算", command=self.parent.refresh_all_calculations).pack(fill=tk.X, padx=5, pady=3)
        ttk.Button(calc_tools, text="压缩空气计算", command=self.parent.show_compressed_air_calculator).pack(fill=tk.X, padx=5, pady=3)
        ttk.Button(calc_tools, text="调节阀计算", command=self.parent.show_valve_calculator).pack(fill=tk.X, padx=5, pady=3)
        ttk.Button(calc_tools, text="自力式阀计算", command=self.parent.show_self_operated_valve_calculator).pack(fill=tk.X, padx=5, pady=3)
        
        # 氧枪和全氧窑按钮
        self.parent.oxygen_lance_button = ttk.Button(calc_tools, text="0#氧枪计算", 
                                           command=self.parent.show_oxygen_lance_calculator, 
                                           state="disabled")
        self.parent.oxygen_lance_button.pack(fill=tk.X, padx=5, pady=3)
        
        self.parent.oxygen_pipe_button = ttk.Button(calc_tools, text="全氧窑氧气管道计算", 
                                          command=self.parent.show_oxygen_pipe_calculator, 
                                          state="disabled")
        self.parent.oxygen_pipe_button.pack(fill=tk.X, padx=5, pady=3)
        
        # 3. 系统选项分组
        system_options = ttk.LabelFrame(menu_container, text="系统选项")
        system_options.pack(fill=tk.X, padx=5, pady=5)
        ttk.Button(system_options, text="设置", command=self.parent.show_settings_window).pack(fill=tk.X, padx=5, pady=3)
        ttk.Button(system_options, text="历史记录管理", command=self.parent.show_history_manager).pack(fill=tk.X, padx=5, pady=3)
        
    def create_tab_interface(self):
        """创建5栏式界面，按照截图布局"""
        # 创建一个框架用于放置所有内容
        self.parent.main_frame = ttk.Frame(self.parent.work_area)
        self.parent.work_area.add(self.parent.main_frame)

        # 创建5个区域的框架
        self.parent.project_info_frame = ttk.LabelFrame(self.parent.main_frame, text="项目信息栏")
        self.parent.flow_frame = ttk.LabelFrame(self.parent.main_frame, text="流量栏")
        self.parent.pressure_frame = ttk.LabelFrame(self.parent.main_frame, text="压力栏")
        self.parent.diameter_frame = ttk.LabelFrame(self.parent.main_frame, text="选取管径栏")
        self.parent.velocity_temp_frame = ttk.LabelFrame(self.parent.main_frame, text="流速及温度栏")

        # 使用grid布局管理器排列这些框架
        self.parent.project_info_frame.grid(row=0, column=0, padx=5, pady=5, sticky="nsew")
        self.parent.flow_frame.grid(row=0, column=1, padx=5, pady=5, sticky="nsew")
        self.parent.pressure_frame.grid(row=0, column=2, padx=5, pady=5, sticky="nsew")
        self.parent.diameter_frame.grid(row=0, column=3, padx=5, pady=5, sticky="nsew")
        self.parent.velocity_temp_frame.grid(row=0, column=4, padx=5, pady=5, sticky="nsew")

        # 配置列的权重，使其平均分配空间
        for i in range(5):
            self.parent.main_frame.grid_columnconfigure(i, weight=1)

        # 在各个区域中创建内容
        self.create_project_info_content()
        self.create_flow_content()
        self.create_pressure_content()
        self.create_diameter_content()
        self.create_velocity_temp_content()
        
    def create_project_info_content(self):
        """创建项目信息栏内容"""
        # 实现项目信息栏的UI创建逻辑
        pass
        
    def create_flow_content(self):
        """创建流量栏内容"""
        # 实现流量栏的UI创建逻辑
        pass
        
    def create_pressure_content(self):
        """创建压力栏内容"""
        # 实现压力栏的UI创建逻辑
        pass
        
    def create_diameter_content(self):
        """创建选取管径栏内容"""
        # 实现选取管径栏的UI创建逻辑
        pass
        
    def create_velocity_temp_content(self):
        """创建流速及温度栏内容"""
        # 实现流速及温度栏的UI创建逻辑
        pass
