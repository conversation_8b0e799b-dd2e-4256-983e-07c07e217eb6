#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
import sys
import traceback

# 添加当前目录到路径
sys.path.append('.')

def test_valve_calculator():
    """测试调节阀计算界面"""
    try:
        print("正在测试调节阀计算界面...")
        
        # 创建主窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        # 导入并创建GasCalculator
        from gas_cal import GasCalculator
        app = GasCalculator(root)
        
        # 测试调节阀计算界面
        print("尝试打开调节阀计算界面...")
        app.show_valve_calculator()
        print("调节阀计算界面打开成功！")
        
        # 等待一段时间然后关闭
        root.after(2000, root.quit)  # 2秒后关闭
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"调节阀计算界面测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_oxygen_pipe_calculator():
    """测试全氧窑氧气计算界面"""
    try:
        print("正在测试全氧窑氧气计算界面...")
        
        # 创建主窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        # 导入并创建GasCalculator
        from gas_cal import GasCalculator
        app = GasCalculator(root)
        
        # 设置为全氧窑模式
        app.is_oxygen_kiln.set("是")
        
        # 测试全氧窑氧气计算界面
        print("尝试打开全氧窑氧气计算界面...")
        app.show_oxygen_pipe_calculator()
        print("全氧窑氧气计算界面打开成功！")
        
        # 等待一段时间然后关闭
        root.after(2000, root.quit)  # 2秒后关闭
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"全氧窑氧气计算界面测试失败: {str(e)}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试界面...")
    
    # 测试调节阀计算界面
    valve_result = test_valve_calculator()
    
    # 测试全氧窑氧气计算界面
    oxygen_result = test_oxygen_pipe_calculator()
    
    # 输出测试结果
    print("\n=== 测试结果 ===")
    print(f"调节阀计算界面: {'✓ 成功' if valve_result else '✗ 失败'}")
    print(f"全氧窑氧气计算界面: {'✓ 成功' if oxygen_result else '✗ 失败'}")
    
    if valve_result and oxygen_result:
        print("所有界面测试通过！")
    else:
        print("部分界面测试失败，请检查错误信息。")
