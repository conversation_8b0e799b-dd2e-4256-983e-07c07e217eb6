import tkinter as tk
from tkinter import ttk

class UILayoutManager:
    """UI布局管理器 - 负责管理UI布局和组件排列"""
    
    def __init__(self, parent):
        self.parent = parent
        
    def arrange_components(self, container, components, layout_type="grid", **kwargs):
        """
        排列组件
        
        参数:
        container - 容器组件
        components - 要排列的组件列表
        layout_type - 布局类型，可以是"grid"、"pack"或"place"
        **kwargs - 传递给布局管理器的额外参数
        """
        if layout_type == "grid":
            rows = kwargs.get("rows", 1)
            cols = kwargs.get("cols", len(components))
            padx = kwargs.get("padx", 5)
            pady = kwargs.get("pady", 5)
            
            # 计算每个组件的位置
            for i, component in enumerate(components):
                row = i // cols
                col = i % cols
                component.grid(row=row, column=col, padx=padx, pady=pady, sticky=kwargs.get("sticky", "nsew"))
                
        elif layout_type == "pack":
            side = kwargs.get("side", "top")
            fill = kwargs.get("fill", "both")
            expand = kwargs.get("expand", True)
            padx = kwargs.get("padx", 5)
            pady = kwargs.get("pady", 5)
            
            for component in components:
                component.pack(side=side, fill=fill, expand=expand, padx=padx, pady=pady)
                
        elif layout_type == "place":
            # 使用相对位置
            for i, component in enumerate(components):
                x = kwargs.get(f"x{i}", 0.1 * i)
                y = kwargs.get(f"y{i}", 0.1 * i)
                relwidth = kwargs.get("relwidth", 0.2)
                relheight = kwargs.get("relheight", 0.2)
                component.place(relx=x, rely=y, relwidth=relwidth, relheight=relheight)
    
    def create_scrollable_frame(self, parent, **kwargs):
        """
        创建一个可滚动的框架
        
        参数:
        parent - 父容器
        **kwargs - 额外参数
        
        返回:
        (canvas, scrollable_frame) - 画布和可滚动框架的元组
        """
        # 创建一个画布和滚动条
        canvas = tk.Canvas(parent, **kwargs)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        
        # 创建一个框架放在画布上
        scrollable_frame = ttk.Frame(canvas)
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        # 将框架放入画布
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 绑定鼠标滚轮事件
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
        canvas.bind_all("<MouseWheel>", _on_mousewheel)
        
        return canvas, scrollable_frame
    
    def create_form_layout(self, parent, labels, variables, entry_width=15, padx=5, pady=5):
        """
        创建表单布局
        
        参数:
        parent - 父容器
        labels - 标签文本列表
        variables - 变量列表
        entry_width - 输入框宽度
        padx, pady - 内边距
        
        返回:
        entries - 输入框列表
        """
        entries = []
        
        for i, (label_text, variable) in enumerate(zip(labels, variables)):
            # 创建标签
            label = ttk.Label(parent, text=label_text)
            label.grid(row=i, column=0, padx=padx, pady=pady, sticky="e")
            
            # 创建输入框
            entry = ttk.Entry(parent, textvariable=variable, width=entry_width)
            entry.grid(row=i, column=1, padx=padx, pady=pady, sticky="w")
            entries.append(entry)
            
        return entries
    
    def create_button_row(self, parent, buttons, side="bottom", padx=5, pady=5, fill="x"):
        """
        创建按钮行
        
        参数:
        parent - 父容器
        buttons - 按钮配置列表，每个元素是(text, command)元组
        side - 放置方向
        padx, pady - 内边距
        fill - 填充方式
        
        返回:
        button_frame - 按钮框架
        """
        button_frame = ttk.Frame(parent)
        button_frame.pack(side=side, fill=fill, padx=padx, pady=pady)
        
        for text, command in buttons:
            ttk.Button(button_frame, text=text, command=command).pack(side="left", padx=padx)
            
        return button_frame
    
    def create_tabbed_interface(self, parent, tab_configs):
        """
        创建选项卡界面
        
        参数:
        parent - 父容器
        tab_configs - 选项卡配置列表，每个元素是(title, content_creator)元组
                     content_creator是一个函数，接受选项卡框架作为参数
        
        返回:
        notebook - 选项卡控件
        """
        notebook = ttk.Notebook(parent)
        notebook.pack(fill="both", expand=True, padx=5, pady=5)
        
        for title, content_creator in tab_configs:
            tab = ttk.Frame(notebook)
            notebook.add(tab, text=title)
            content_creator(tab)
            
        return notebook