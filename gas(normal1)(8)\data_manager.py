import json
from datetime import datetime

class DataManager:
    """数据管理器 - 负责数据的收集、整理和管理"""
    
    def __init__(self, parent):
        self.parent = parent
    
    def collect_project_data(self):
        """收集项目数据"""
        try:
            project_data = {
                "时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "工程名称": self.parent.project_name.get(),
                "工程代号": self.parent.project_code.get(),
                "项目类型": self.parent.project_type.get(),
                "吨位": self.parent.daily_capacity.get(),
                "小炉数": self.parent.furnace_count.get(),
                "一窑几线": self.parent.line_count.get(),
                "是否有0#氧枪": self.parent.has_oxygen_lance.get(),
                "是否是全氧窑": self.parent.is_oxygen_kiln.get(),
            }
            
            return project_data
        except Exception as e:
            print(f"收集项目数据时出错: {str(e)}")
            return {}
    
    def collect_flow_data(self):
        """收集流量数据"""
        try:
            flow_data = {
                "窑老期流量": self.parent.old_flow.get(),
                "正常生产时流量": self.parent.normal_flow.get(),
                "成型室加热流量": self.parent.forming_flow.get(),
                "边火加热流量": self.parent.edge_flow.get(),
                "支通路总流量": self.parent.bypass_total_flow.get()
            }
            return flow_data
        except Exception as e:
            print(f"收集流量数据时出错: {str(e)}")
            return {}
    
    def collect_pressure_data(self):
        """收集压力数据"""
        try:
            pressure_data = {
                "进车间压力": self.parent.inlet_pressure.get(),
                "总管调节阀前压力": self.parent.main_valve_pre.get(),
                "总管调节阀后压力": self.parent.main_valve_post.get(),
                "小炉支管调节阀前压力": self.parent.branch_valve_pre.get(),
                "小炉支管调节阀后压力": self.parent.branch_valve_post.get(),
                "支通路总管阀前压力": self.parent.bypass_main_valve_pre.get(),
                "支通路总管阀后压力": self.parent.bypass_main_valve_post.get(),
            }
            
            # 添加支通路压力数据
            if hasattr(self.parent, 'bypass_vars') and self.parent.bypass_vars:
                pressure_data["边火阀前压力"] = self.parent.bypass_vars['branch_pre']['pressure'].get()
                pressure_data["边火阀后压力"] = self.parent.bypass_vars['branch_post']['pressure'].get()
            
            return pressure_data
        except Exception as e:
            print(f"收集压力数据时出错: {str(e)}")
            return {}
    
    def collect_diameter_data(self):
        """收集管径数据"""
        try:
            diameter_data = {
                "总管阀前管径": self.parent.main_pre_diameter.get(),
                "总管阀后管径": self.parent.main_post_diameter.get(),
                "支通路总管阀前管径": self.parent.bypass_main_pre_diameter.get(),
                "支通路总管阀后管径": self.parent.bypass_main_post_diameter.get(),
            }
            
            # 添加支通路管径数据
            if hasattr(self.parent, 'bypass_vars') and self.parent.bypass_vars:
                diameter_data["支通路支管阀前管径"] = self.parent.bypass_vars['branch_pre']['selected'].get()
                diameter_data["支通路支管阀后管径"] = self.parent.bypass_vars['branch_post']['selected'].get()
            
            return diameter_data
        except Exception as e:
            print(f"收集管径数据时出错: {str(e)}")
            return {}
    
    def collect_velocity_temperature_data(self):
        """收集流速和温度数据"""
        try:
            velocity_temp_data = {
                "天然气总管流速": self.parent.main_velocity.get(),
                "小炉支管流速": self.parent.branch_velocity.get(),
                "设计温度": self.parent.temperature.get()
            }
            return velocity_temp_data
        except Exception as e:
            print(f"收集流速温度数据时出错: {str(e)}")
            return {}
    
    def collect_valve_data(self):
        """收集阀门数据"""
        try:
            valve_data = {
                "主管调节阀C选定": self.parent.main_valve_c_selected_value.get(),
                "主管调节阀K最大值": self.parent.main_valve_k_max_value.get(),
                "主管调节阀K最小值": self.parent.main_valve_k_min_value.get(),
                "主管调节阀阀芯尺寸": self.parent.main_valve_core_size_value.get(),
                "主管调节阀管径": self.parent.main_valve_diameter_value.get(),
                "放散阀工作压力": self.parent.release_valve_working_pressure_value.get(),
                "放散阀反算d0": self.parent.release_valve_d0_value.get(),
                "放散阀选取DN": self.parent.release_valve_dn_value.get(),
                "放散阀截面积A": self.parent.release_valve_area_value.get(),
            }
            
            # 添加计算值
            if hasattr(self.parent, 'main_valve_c_large'):
                valve_data["主管调节阀C计算大"] = self.parent.main_valve_c_large.get()
            if hasattr(self.parent, 'main_valve_c_small'):
                valve_data["主管调节阀C计算小"] = self.parent.main_valve_c_small.get()
            
            return valve_data
        except Exception as e:
            print(f"收集阀门数据时出错: {str(e)}")
            return {}
    
    def collect_furnace_data(self):
        """收集小炉数据"""
        try:
            furnace_data_list = []
            
            if hasattr(self.parent, 'furnace_data') and self.parent.furnace_data:
                for furnace in self.parent.furnace_data:
                    furnace_info = {
                        "平均热负荷": furnace['heat_load'].get(),
                        "浮动值": furnace['float_value'].get(),
                        "喷枪数": furnace['nozzle_count'].get(),
                    }
                    
                    # 添加计算结果
                    if 'max_flow' in furnace:
                        furnace_info["最大流量"] = furnace['max_flow'].get()
                    if 'normal_flow' in furnace:
                        furnace_info["正常流量"] = furnace['normal_flow'].get()
                    if 'min_flow' in furnace:
                        furnace_info["最小流量"] = furnace['min_flow'].get()
                    
                    furnace_data_list.append(furnace_info)
            
            return furnace_data_list
        except Exception as e:
            print(f"收集小炉数据时出错: {str(e)}")
            return []
    
    def collect_air_data(self):
        """收集压缩空气数据"""
        try:
            air_data = {
                "进车间压力(MPa)": self.parent.air_pressure.get(),
                "设计流速(m/s)": self.parent.air_velocity.get(),
                "连续用气总量(Nm³/h)": self.parent.air_continuous_total.get(),
                "间歇用气总量(Nm³/h)": self.parent.air_intermittent_total.get(),
                "总用气量(Nm³/h)": self.parent.air_total_flow.get(),
                "计算管径(mm)": self.parent.air_calculated_diameter.get(),
                "选取管径(mm)": self.parent.air_standard_diameter.get(),
                "实际流速(m/s)": self.parent.air_actual_velocity.get(),
            }
            
            # 添加用气明细数据
            if hasattr(self.parent, 'air_input_vars'):
                air_data["蓄热室吹扫用气(间歇Nm³/h)"] = self.parent.air_input_vars["regenerator_purge"].get()
                air_data["喷枪冷却用气(连续Nm³/h)"] = self.parent.air_input_vars["gun_cooling"].get()
                air_data["投料机用气(连续Nm³/h)"] = self.parent.air_input_vars["feeder"].get()
                air_data["阀及工业电视用气(连续Nm³/h)"] = self.parent.air_input_vars["valve_tv"].get()
                air_data["冷端机组用气(连续Nm³/h)"] = self.parent.air_input_vars["cold_end"].get()
                air_data["退火窑及红外用气(连续Nm³/h)"] = self.parent.air_input_vars["annealing_ir"].get()
                air_data["支通路加热用气(间歇Nm³/h)"] = self.parent.air_input_vars["branch_heating"].get()
                air_data["压延机烧边火用气(连续Nm³/h)"] = self.parent.air_input_vars["rolling_burn"].get()
                air_data["压延机清理用气(间歇Nm³/h)"] = self.parent.air_input_vars["rolling_clean"].get()
            
            return air_data
        except Exception as e:
            print(f"收集压缩空气数据时出错: {str(e)}")
            return {}
    
    def collect_oxygen_data(self):
        """收集氧气数据"""
        try:
            oxygen_data = {}
            
            # 收集氧枪氧气数据
            if hasattr(self.parent, 'o2_flow'):
                oxygen_data.update({
                    "氧气流量": self.parent.o2_flow.get(),
                    "氧气进车间压力": self.parent.o2_inlet_pressure.get(),
                    "氧气主管阀前压力": self.parent.o2_main_valve_pre.get(),
                    "氧气主管阀后压力": self.parent.o2_main_valve_post.get(),
                    "氧气支管阀前压力": self.parent.o2_branch_valve_pre.get(),
                    "氧气支管阀后压力": self.parent.o2_branch_valve_post.get(),
                    "氧气密度": self.parent.o2_density.get(),
                    "氧气流速": self.parent.o2_velocity.get(),
                })
            
            # 收集全氧窑氧气数据
            if hasattr(self.parent, 'oxygen_flow'):
                oxygen_data.update({
                    "氧气正常流量(Nm³/h)": self.parent.oxygen_flow.get(),
                    "窑老期流量(Nm³/h)": self.parent.oxygen_old_flow.get(),
                    "氧气进车间压力(MPa)": self.parent.oxygen_inlet_pressure.get(),
                    "氧气总管调节阀前压力(MPa)": self.parent.oxygen_main_pre_pressure.get(),
                    "氧气总管调节阀后压力(MPa)": self.parent.oxygen_main_post_pressure.get(),
                    "氧气支管阀前压力(MPa)": self.parent.oxygen_branch_pre_pressure.get(),
                    "氧气支管阀后压力(MPa)": self.parent.oxygen_branch_post_pressure.get(),
                    "氧气设计流速(m/s)": self.parent.oxygen_velocity.get(),
                    "氧气设计温度(℃)": self.parent.oxygen_temperature.get(),
                })
            
            return oxygen_data
        except Exception as e:
            print(f"收集氧气数据时出错: {str(e)}")
            return {}
    
    def collect_natural_gas_data(self):
        """收集天然气数据"""
        try:
            ng_data = {}
            
            if hasattr(self.parent, 'ng_flow'):
                ng_data.update({
                    "天然气流量(Nm³/h)": self.parent.ng_flow.get(),
                    "天然气进车间压力(MPa)": self.parent.ng_inlet_pressure.get(),
                    "天然气主管阀前压力(MPa)": self.parent.ng_main_valve_pre.get(),
                    "天然气主管阀后压力(MPa)": self.parent.ng_main_valve_post.get(),
                    "天然气支管阀前压力(MPa)": self.parent.ng_branch_valve_pre.get(),
                    "天然气支管阀后压力(MPa)": self.parent.ng_branch_valve_post.get(),
                    "天然气密度(kg/m³)": self.parent.ng_density.get(),
                    "天然气流速(m/s)": self.parent.ng_velocity.get(),
                })
            
            return ng_data
        except Exception as e:
            print(f"收集天然气数据时出错: {str(e)}")
            return {}
    
    def collect_all_data(self):
        """收集所有数据"""
        try:
            all_data = {}
            
            # 收集各类数据
            all_data.update(self.collect_project_data())
            all_data["流量数据"] = self.collect_flow_data()
            all_data["压力数据"] = self.collect_pressure_data()
            all_data["管径数据"] = self.collect_diameter_data()
            all_data["流速温度数据"] = self.collect_velocity_temperature_data()
            all_data["阀门数据"] = self.collect_valve_data()
            all_data["小炉数据"] = self.collect_furnace_data()
            
            # 根据项目类型收集特定数据
            if self.parent.has_oxygen_lance.get() == "是":
                all_data["氧枪数据"] = self.collect_oxygen_data()
                all_data["天然气数据"] = self.collect_natural_gas_data()
            
            # 收集压缩空气数据
            air_data = self.collect_air_data()
            if any(air_data.values()):  # 如果有压缩空气数据
                all_data.update(air_data)
                all_data["计算类型"] = "压缩空气管道计算"
            
            return all_data
        except Exception as e:
            print(f"收集所有数据时出错: {str(e)}")
            return {}
    
    def validate_data(self, data):
        """验证数据完整性"""
        try:
            required_fields = ["工程名称", "工程代号"]
            missing_fields = []
            
            for field in required_fields:
                if field not in data or not data[field]:
                    missing_fields.append(field)
            
            if missing_fields:
                return False, f"缺少必填字段: {', '.join(missing_fields)}"
            
            return True, ""
        except Exception as e:
            return False, f"验证数据时出错: {str(e)}"
