import math

class CalculationEngine:
    """计算引擎 - 负责各种工程计算"""
    
    def __init__(self, parent):
        self.parent = parent
    
    def calculate_pipe_diameter(self, flow, pressure, velocity, temperature=0):
        """计算管径
        Args:
            flow: 流量(Nm³/h)
            pressure: 压力(MPa)
            velocity: 流速(m/s)
            temperature: 温度(℃)
        Returns:
            diameter: 计算管径(mm)
        """
        try:
            if flow <= 0 or pressure <= 0 or velocity <= 0:
                return 0
            
            # 使用标准管径计算公式
            diameter = 18.8 * math.sqrt(
                (flow * (273 + temperature) / 2730 / pressure) / velocity
            )
            return diameter
        except Exception as e:
            print(f"计算管径时出错: {str(e)}")
            return 0
    
    def calculate_velocity(self, flow, diameter, pressure, temperature=0):
        """计算流速
        Args:
            flow: 流量(Nm³/h)
            diameter: 管径(mm)
            pressure: 压力(MPa)
            temperature: 温度(℃)
        Returns:
            velocity: 流速(m/s)
        """
        try:
            if flow <= 0 or diameter <= 0 or pressure <= 0:
                return 0
            
            # 计算流速
            velocity = (flow * (273 + temperature) / 2730 / pressure) / \
                      ((diameter / 18.8) * (diameter / 18.8))
            return velocity
        except Exception as e:
            print(f"计算流速时出错: {str(e)}")
            return 0
    
    def calculate_valve_c_value(self, flow, pre_pressure, post_pressure, temperature=0):
        """计算调节阀C值
        Args:
            flow: 流量(Nm³/h)
            pre_pressure: 阀前压力(MPa)
            post_pressure: 阀后压力(MPa)
            temperature: 温度(℃)
        Returns:
            c_value: C值
        """
        try:
            if flow <= 0 or pre_pressure <= 0 or post_pressure <= 0 or pre_pressure <= post_pressure:
                return 0
            
            # 计算压力差
            delta_p = (pre_pressure - post_pressure) * 10
            
            # 将压力单位从MPa转换为bar (1 MPa = 10 bar)
            pre_pressure_bar = pre_pressure * 10 + 1
            
            # 计算C值
            c_value = 1.167 * flow * math.sqrt(0.743 * (273 + temperature)) / 514 / \
                     (1 - 0.46 * delta_p / pre_pressure_bar) / \
                     math.sqrt(delta_p * pre_pressure_bar)
            
            return c_value
        except Exception as e:
            print(f"计算C值时出错: {str(e)}")
            return 0
    
    def calculate_valve_k_value(self, c_calculated, c_selected):
        """计算调节阀K值(开度)
        Args:
            c_calculated: 计算C值
            c_selected: 选定C值
        Returns:
            k_value: K值(%)
        """
        try:
            if c_selected <= 0:
                return 0
            
            k_value = (c_calculated / c_selected) * 100
            return min(k_value, 100)  # 限制最大值为100%
        except Exception as e:
            print(f"计算K值时出错: {str(e)}")
            return 0
    
    def calculate_furnace_flow(self, normal_flow, heat_load, float_value, flow_type="max"):
        """计算小炉流量
        Args:
            normal_flow: 正常生产流量(Nm³/h)
            heat_load: 平均热负荷(%)
            float_value: 浮动值(%)
            flow_type: 流量类型("max", "normal", "min")
        Returns:
            flow: 计算流量(Nm³/h)
        """
        try:
            if normal_flow <= 0 or heat_load <= 0:
                return 0
            
            if flow_type == "max":
                # 最大流量 = 窑老期流量 × (平均热负荷% + 浮动值%)
                flow = normal_flow * (heat_load + float_value) / 100
            elif flow_type == "normal":
                # 正常流量 = 正常生产流量 × 平均热负荷%
                flow = normal_flow * heat_load / 100
            elif flow_type == "min":
                # 最小流量 = 正常生产流量 × (平均热负荷% - 浮动值%)
                flow = normal_flow * (heat_load - float_value) / 100
            else:
                flow = 0
            
            return max(flow, 0)  # 确保流量不为负数
        except Exception as e:
            print(f"计算小炉流量时出错: {str(e)}")
            return 0
    
    def calculate_pressure_drop(self, flow, diameter, length, roughness=0.045):
        """计算管道压降
        Args:
            flow: 流量(Nm³/h)
            diameter: 管径(mm)
            length: 管道长度(m)
            roughness: 粗糙度(mm)
        Returns:
            pressure_drop: 压降(Pa)
        """
        try:
            if flow <= 0 or diameter <= 0 or length <= 0:
                return 0
            
            # 简化的压降计算公式
            # 实际应用中需要根据具体的流体和管道条件调整
            velocity = flow / (3600 * math.pi * (diameter/2000)**2)
            reynolds = velocity * diameter / 1000 / 1.5e-5  # 假设运动粘度
            
            if reynolds > 2300:  # 湍流
                friction_factor = 0.316 / (reynolds**0.25)
            else:  # 层流
                friction_factor = 64 / reynolds
            
            pressure_drop = friction_factor * length / (diameter/1000) * \
                           1.225 * velocity**2 / 2  # 假设密度1.225 kg/m³
            
            return pressure_drop
        except Exception as e:
            print(f"计算压降时出错: {str(e)}")
            return 0
    
    def get_standard_diameter(self, calculated_diameter):
        """获取标准管径
        Args:
            calculated_diameter: 计算管径(mm)
        Returns:
            standard_diameter: 标准管径(mm)
        """
        # 标准管径系列
        standard_diameters = [
            15, 20, 25, 32, 40, 50, 65, 80, 100, 125, 150, 200, 250, 300, 350, 400, 450, 500, 600, 700, 800, 900, 1000
        ]
        
        # 找到大于等于计算管径的最小标准管径
        for diameter in standard_diameters:
            if diameter >= calculated_diameter:
                return diameter
        
        # 如果计算管径超过最大标准管径，返回最大值
        return standard_diameters[-1]
    
    def get_standard_c_value(self, calculated_c):
        """获取标准C值
        Args:
            calculated_c: 计算C值
        Returns:
            standard_c: 标准C值
        """
        # 标准C值系列
        standard_c_values = [0.28, 0.52, 0.93, 1.6, 2.5, 4, 8, 13, 20, 25, 45, 72, 100, 175, 280, 365, 640]
        
        # 找到大于等于计算C值的最小标准C值
        for c_value in standard_c_values:
            if c_value >= calculated_c:
                return c_value
        
        # 如果计算C值超过最大标准C值，返回最大值
        return standard_c_values[-1]
    
    def calculate_working_flow(self, standard_flow, pressure, temperature, standard_pressure=0.1013, standard_temperature=0):
        """计算工作状态下流量
        Args:
            standard_flow: 标准状态流量(Nm³/h)
            pressure: 工作压力(MPa)
            temperature: 工作温度(℃)
            standard_pressure: 标准压力(MPa)
            standard_temperature: 标准温度(℃)
        Returns:
            working_flow: 工作状态流量(m³/h)
        """
        try:
            if standard_flow <= 0:
                return 0
            
            # 根据理想气体状态方程计算工作状态流量
            working_flow = standard_flow * (standard_pressure / pressure) * \
                          ((273 + temperature) / (273 + standard_temperature))
            
            return working_flow
        except Exception as e:
            print(f"计算工作状态流量时出错: {str(e)}")
            return 0
    
    def validate_calculation_inputs(self, **kwargs):
        """验证计算输入参数
        Returns:
            is_valid: 是否有效
            error_message: 错误信息
        """
        try:
            for key, value in kwargs.items():
                if value is None or value == "":
                    return False, f"{key} 不能为空"
                
                try:
                    float_value = float(value)
                    if float_value < 0:
                        return False, f"{key} 不能为负数"
                except ValueError:
                    return False, f"{key} 必须是数字"
            
            return True, ""
        except Exception as e:
            return False, f"验证输入时出错: {str(e)}"
