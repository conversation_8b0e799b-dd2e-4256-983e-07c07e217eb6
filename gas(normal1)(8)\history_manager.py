import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
from datetime import datetime

class HistoryManager:
    def __init__(self, parent):
        self.parent = parent
        self.history_window = None
        
    def show_history_manager(self):
        """显示历史记录管理窗口"""
        # 如果窗口已经存在，则聚焦
        if self.history_window and self.history_window.winfo_exists():
            self.history_window.focus_set()
            return
            
        # 创建新窗口
        self.history_window = tk.Toplevel(self.parent.root)
        self.history_window.title("历史记录管理")
        self.history_window.geometry("800x500")
        self.history_window.resizable(True, True)
        
        # 创建主框架
        main_frame = ttk.Frame(self.history_window)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 创建搜索框架
        search_frame = ttk.LabelFrame(main_frame, text="搜索")
        search_frame.pack(fill="x", padx=5, pady=5)
        
        # 创建搜索输入框和按钮
        ttk.Label(search_frame, text="关键词:").grid(row=0, column=0, padx=5, pady=5)
        search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=search_var, width=30)
        search_entry.grid(row=0, column=1, padx=5, pady=5)
        
        # 创建历史记录表格
        history_frame = ttk.LabelFrame(main_frame, text="历史记录")
        history_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 创建Treeview用于显示历史记录
        columns = ("时间", "工程名称", "工程代号", "项目类型")
        history_tree = ttk.Treeview(history_frame, columns=columns, show="headings")
        
        # 设置列标题
        for col in columns:
            history_tree.heading(col, text=col)
            history_tree.column(col, width=100)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(history_frame, orient="vertical", command=history_tree.yview)
        history_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side="right", fill="y")
        history_tree.pack(side="left", fill="both", expand=True)
        
        # 加载历史记录
        history = []
        try:
            if os.path.exists(self.parent.history_file):
                with open(self.parent.history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
        except Exception as e:
            messagebox.showerror("错误", f"加载历史记录时出错: {str(e)}")
        
        # 搜索功能
        def search_records():
            # 清空当前显示
            for item in history_tree.get_children():
                history_tree.delete(item)
                
            # 获取搜索关键词
            keyword = search_var.get().lower()
            
            # 过滤并显示匹配的记录
            for record in history:
                # 检查记录是否包含关键词
                if (keyword in str(record.get("工程名称", "")).lower() or
                    keyword in str(record.get("工程代号", "")).lower() or
                    keyword in str(record.get("项目类型", "")).lower() or
                    not keyword):  # 如果关键词为空，显示所有记录
                    
                    # 插入记录到表格
                    history_tree.insert("", "end", values=(
                        record.get("时间", ""),
                        record.get("工程名称", ""),
                        record.get("工程代号", ""),
                        record.get("项目类型", "")
                    ))
        
        # 绑定搜索按钮
        ttk.Button(search_frame, text="搜索", command=search_records).grid(row=0, column=2, padx=5, pady=5)
        
        # 创建操作按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", padx=5, pady=5)
        
        # 加载按钮
        def load_selected_record():
            selected = history_tree.selection()
            if not selected:
                messagebox.showinfo("提示", "请先选择一条记录")
                return
                
            # 获取选中记录的数据
            values = history_tree.item(selected[0], "values")
            if not values or len(values) < 2:
                return
                
            # 从历史数据中查找匹配记录
            for record in history:
                if (record.get("时间") == values[0] and
                    record.get("工程名称") == values[1] and
                    record.get("工程代号") == values[2]):
                    
                    # 加载记录
                    self.parent.load_project_data(record)
                    messagebox.showinfo("成功", "项目已加载")
                    self.history_window.destroy()
                    return
                    
            messagebox.showinfo("提示", "未找到匹配的历史记录")
        
        # 删除按钮
        def delete_selected_record():
            selected = history_tree.selection()
            if not selected:
                messagebox.showinfo("提示", "请先选择一条记录")
                return
                
            # 确认删除
            if not messagebox.askyesno("确认", "确定要删除选中的记录吗？"):
                return
                
            # 获取选中记录的数据
            values = history_tree.item(selected[0], "values")
            if not values or len(values) < 2:
                return
                
            # 从历史数据中查找并删除匹配记录
            for i, record in enumerate(history):
                if (record.get("时间") == values[0] and
                    record.get("工程名称") == values[1] and
                    record.get("工程代号") == values[2]):
                    
                    # 删除记录
                    del history[i]
                    
                    # 保存更新后的历史记录
                    try:
                        with open(self.parent.history_file, 'w', encoding='utf-8') as f:
                            json.dump(history, f, ensure_ascii=False, indent=2)
                            
                        # 更新显示
                        search_records()
                        messagebox.showinfo("成功", "记录已删除")
                    except Exception as e:
                        messagebox.showerror("错误", f"保存更新时出错: {str(e)}")
                        
                    return
                    
            messagebox.showinfo("提示", "未找到匹配的历史记录")
        
        # 编辑按钮
        def edit_selected_record():
            selected = history_tree.selection()
            if not selected:
                messagebox.showinfo("提示", "请先选择一条记录")
                return

            # 获取选中记录的数据
            values = history_tree.item(selected[0], "values")
            if not values or len(values) < 2:
                return

            # 从历史数据中查找匹配记录
            for i, record in enumerate(history):
                if (record.get("时间") == values[0] and
                    record.get("工程名称") == values[1] and
                    record.get("工程代号") == values[2]):

                    # 创建编辑窗口
                    edit_window = tk.Toplevel(self.history_window)
                    edit_window.title("编辑历史记录")
                    edit_window.geometry("400x300")
                    edit_window.resizable(False, False)

                    # 创建编辑表单
                    ttk.Label(edit_window, text="工程名称:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
                    project_name_var = tk.StringVar(value=record.get("工程名称", ""))
                    ttk.Entry(edit_window, textvariable=project_name_var, width=30).grid(row=0, column=1, padx=5, pady=5, sticky="w")

                    ttk.Label(edit_window, text="工程代号:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
                    project_code_var = tk.StringVar(value=record.get("工程代号", ""))
                    ttk.Entry(edit_window, textvariable=project_code_var, width=30).grid(row=1, column=1, padx=5, pady=5, sticky="w")

                    ttk.Label(edit_window, text="项目类型:").grid(row=2, column=0, padx=5, pady=5, sticky="w")
                    project_type_var = tk.StringVar(value=record.get("项目类型", ""))
                    ttk.Entry(edit_window, textvariable=project_type_var, width=30).grid(row=2, column=1, padx=5, pady=5, sticky="w")

                    # 保存按钮
                    def save_edit():
                        # 更新记录
                        record["工程名称"] = project_name_var.get()
                        record["工程代号"] = project_code_var.get()
                        record["项目类型"] = project_type_var.get()

                        # 保存更新后的历史记录
                        try:
                            with open(self.parent.history_file, 'w', encoding='utf-8') as f:
                                json.dump(history, f, ensure_ascii=False, indent=2)

                            # 更新显示
                            search_records()
                            messagebox.showinfo("成功", "记录已更新")
                            edit_window.destroy()
                        except Exception as e:
                            messagebox.showerror("错误", f"保存更新时出错: {str(e)}")

                    ttk.Button(edit_window, text="保存", command=save_edit).grid(row=3, column=0, columnspan=2, padx=5, pady=10)

                    # 设置窗口居中
                    self.center_window(edit_window)
                    return

            messagebox.showinfo("提示", "未找到匹配的历史记录")
        
        # 添加按钮
        ttk.Button(button_frame, text="加载", command=load_selected_record).pack(side="left", padx=5)
        ttk.Button(button_frame, text="编辑", command=edit_selected_record).pack(side="left", padx=5)
        ttk.Button(button_frame, text="删除", command=delete_selected_record).pack(side="left", padx=5)
        ttk.Button(button_frame, text="关闭", command=self.history_window.destroy).pack(side="right", padx=5)
        
        # 初始显示所有记录
        search_records()
        
    def center_window(self, window):
        """将窗口居中显示"""
        window.update_idletasks()
        width = window.winfo_width()
        height = window.winfo_height()
        x = (window.winfo_screenwidth() // 2) - (width // 2)
        y = (window.winfo_screenheight() // 2) - (height // 2)
        window.geometry('{}x{}+{}+{}'.format(width, height, x, y))