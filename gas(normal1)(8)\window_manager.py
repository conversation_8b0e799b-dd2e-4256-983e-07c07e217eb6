import tkinter as tk
from tkinter import ttk, messagebox

class WindowManager:
    """窗口管理器 - 负责管理所有弹出窗口"""
    
    def __init__(self, parent):
        self.parent = parent
        self.open_windows = {}  # 用于存储所有打开的窗口
        
    def register_window(self, window_name, window):
        """注册一个窗口到管理器"""
        self.open_windows[window_name] = window
        
    def unregister_window(self, window_name):
        """从管理器中注销一个窗口"""
        if window_name in self.open_windows:
            del self.open_windows[window_name]
            
    def close_all_windows(self):
        """关闭所有已注册的窗口"""
        for window_name, window in list(self.open_windows.items()):
            try:
                if window and window.winfo_exists():
                    window.destroy()
            except:
                pass
        self.open_windows.clear()
        
    def get_window(self, window_name):
        """获取指定名称的窗口"""
        return self.open_windows.get(window_name)
        
    def is_window_open(self, window_name):
        """检查指定名称的窗口是否打开"""
        window = self.get_window(window_name)
        return window is not None and window.w